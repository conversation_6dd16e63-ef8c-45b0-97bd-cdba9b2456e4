package com.wexl.dps.service;

import com.wexl.dps.dto.ErpIntegrationDto;
import com.wexl.retail.guardian.dto.GuardianRequest;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ParentProcessor implements CommandProcessor {
  private final UserRepository userRepository;
  private final GuardianRepository guardianRepository;
  private final GuardianService guardianService;

  public void process(ErpIntegrationDto.DpsEntityChange request) {
    switch (request.changeType()) {
      case "ADD" -> createGuardian(request);
      case "DELETE" -> deleteGuardian(request);
      case "UPDATE" -> updateGuardian(request);
      default -> log.error("change Type not found");
    }
  }

  private void createGuardian(ErpIntegrationDto.DpsEntityChange request) {
    try {
      List<String> studentCodes = getStudentCodes(request);
      for (String student : studentCodes) {
        Optional<User> possibleUser = userRepository.findByExternalRef(student);
        if (possibleUser.isEmpty()) {
          log.info("Unable to find student with code {}", student);
        } else {
          guardianService.createGuardian(
              possibleUser.get().getAuthUserId(),
              Collections.singletonList(buildParentRequest(request)));
        }
      }
    } catch (Exception e) {
      log.error("Error in creating a guardian [" + e.getMessage() + "]", e);
    }
  }

  private void updateGuardian(ErpIntegrationDto.DpsEntityChange request) {
    try {
      List<String> studentCodes = getStudentCodes(request);
      for (String student : studentCodes) {
        var user = userRepository.findByExternalRef(student).orElseThrow();
        Optional<User> possibleUser = userRepository.findByExternalRef(student);
        if (possibleUser.isEmpty()) {
          log.info("Unable to find student with code {}", student);
        } else {
          var guardian = guardianRepository.findByStudentId(user.getStudentInfo().getId());
          guardianService.editGuardian(
              buildParentRequest(request), guardian.getFirst().getId(), user.getAuthUserId());
        }
      }
    } catch (Exception e) {
      log.error("Error in updating a guardian [" + e.getMessage() + "]");
    }
  }

  private void deleteGuardian(ErpIntegrationDto.DpsEntityChange request) {
    try {
      List<String> studentCodes = getStudentCodes(request);
      for (String student : studentCodes) {
        var possibleUser = userRepository.findByExternalRef(student);
        if (possibleUser.isEmpty()) {
          log.info("Unable to find student with code {}", student);
        } else {
          var students = possibleUser.get().getStudentInfo();
          var isGuardianExits = guardianRepository.findByStudentAndIsPrimary(students, true);
          guardianRepository.delete(isGuardianExits);
        }
      }
    } catch (Exception e) {
      log.error("Error in deleting a guardian [" + e.getMessage() + "]");
    }
  }

  private List<String> getStudentCodes(ErpIntegrationDto.DpsEntityChange request) {
    List<String> studentCodes = new ArrayList<>();
    if (Objects.nonNull(request.parentResponse().studentCode1())) {
      studentCodes.add(request.parentResponse().studentCode1());
    }
    if (Objects.nonNull(request.parentResponse().studentCode2())) {
      studentCodes.add(request.parentResponse().studentCode2());
    }
    if (Objects.nonNull(request.parentResponse().studentCode3())) {
      studentCodes.add(request.parentResponse().studentCode3());
    }
    return studentCodes;
  }

  private GuardianRequest buildParentRequest(ErpIntegrationDto.DpsEntityChange request) {
    var parentRequest = request.parentResponse();
    return GuardianRequest.builder()
        .firstName(parentRequest.parentName())
        .lastName("")
        .relationType(GuardianRole.FATHER)
        .emailId(parentRequest.email())
        .isPrimary(true)
        .countryCodeId(1L)
        .mobileNumber(request.parentResponse().phone())
        .build();
  }
}
