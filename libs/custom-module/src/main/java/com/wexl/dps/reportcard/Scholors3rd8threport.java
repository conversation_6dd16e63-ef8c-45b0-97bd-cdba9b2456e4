package com.wexl.dps.reportcard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.Scholors3rd8threportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.ResourceUtils;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.InterOverAllReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class Scholors3rd8threport extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final UserService userService;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;

  @Value("classpath:scholors-coscholastic-comprehensive.json")
  private Resource resource;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var requestBody = Scholors3rd8threportDto.Body.builder().build();
    var header = buildReportCardHeader(user);
    var body = buildBody(user, org.getSlug(), request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  private Scholors3rd8threportDto.Header buildReportCardHeader(User user) {
    return Scholors3rd8threportDto.Header.builder()
        .schoolName("THE SCHOLARS' HOME")
        .address("JAMNIWALA ROAD, BADRIPUR, PAONTA SAHIB DIST.SIRMOUR(H.P.)-173025")
        .build();
  }

  private Scholors3rd8threportDto.Body buildBody(
      User user, String orgSlug, ReportCardDto.Request request) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var gradeSlug = student.getSection().getGradeSlug();
    var mother =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findFirst();
    var father =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findFirst();
    var motherName = mother == null ? null : reportCardService.getGuardianName(mother.get());
    var fatherName = father == null ? null : reportCardService.getGuardianName(father.get());
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");

    var data = getData(student);
    var scholasticMandatory = buildScholosticMandatory(data);
    var scholasticOptional = buildScholosticOptional(data);
    var getMarksResponse = getMarksResponse(scholasticMandatory, scholasticOptional);
    //    var studentData = progressCardRepository.findByStudentId(student.getId());
    assert getMarksResponse != null;
    return Scholors3rd8threportDto.Body.builder()
        .name(userService.getNameByUserInfo(user))
        .admissionNumber(student.getRollNumber())
        .rollNo(student.getClassRollNumber())
        .fatherName(fatherName)
        .motherName(motherName)
        .gradeSlug(gradeSlug)
        .sectionName(student.getSection().getName())
        .height(null)
        .weight(null)
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .scholosticMandatory(scholasticMandatory)
        .scholosticTotalPercentage(buildScholosticTotalPercentage(getMarksResponse))
        .scholosticTotalGrade(buildScholosticTotalGrade(getMarksResponse))
        .scholosticOptional(scholasticOptional)
        .attendance(
            request.attendance() != 0.0
                ? request.attendance()
                : attendance(orgSlug, student.getId()))
        .coScholosticMandatory(buildCoScholosticMandatory(data, gradeSlug))
        .coScholosticOptional(buildCoScholosticOptional(data))
        .remarks(request.remarks())
        .issueDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")))
        .classTeacherName(
            student.getSection().getClassTeacher() != null
                ? userService.getNameByUserInfo(
                    student.getSection().getClassTeacher().getUserInfo())
                : null)
        .build();
  }

  private List<Scholors3rd8threportDto.ScholasticMandatory> buildScholosticMandatory(
      List<LowerGradeReportCardData> data) {
    var scholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var scholasticDataMap =
        scholasticMandatoryData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    List<Scholors3rd8threportDto.ScholasticMandatory> scholasticMandatory = new ArrayList<>();
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var port = getMarks(List.of("portfolio"), scholasticData);
          var se = getMarks(List.of("se"), scholasticData);
          var hye = getMarks(List.of("hye"), scholasticData);
          var pt = getPtMarks(List.of("pa1", "pa2", "pa3"), scholasticData);
          var ma = getMarks(List.of("ma"), scholasticData);
          var totalScoredMarks = sumMarks(port, se, hye, pt, ma);
          var totalMarks = 100.0;
          var grade = calculateGrade(totalScoredMarks, totalMarks, null);

          scholasticMandatory.add(
              Scholors3rd8threportDto.ScholasticMandatory.builder()
                  .subject(subject)
                  .portfolio(port)
                  .se(se)
                  .hye(hye)
                  .pt(pt)
                  .ma(ma)
                  .total(totalScoredMarks)
                  .grade(grade)
                  .build());
        });
    return scholasticMandatory;
  }

  private List<Scholors3rd8threportDto.ScholasticOptional> buildScholosticOptional(
      List<LowerGradeReportCardData> data) {
    var scholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var scholasticDataMap =
        scholasticOptionalData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    List<Scholors3rd8threportDto.ScholasticOptional> scholasticOptional = new ArrayList<>();
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var theoryMarks = getMarks(List.of("theory"), scholasticData);
          var practicalMarks = getMarks(List.of("practical"), scholasticData);
          var totalScoredMarks = sumMarks(theoryMarks, practicalMarks);
          var grade = calculateGrade(totalScoredMarks, 50d, null);

          scholasticOptional.add(
              Scholors3rd8threportDto.ScholasticOptional.builder()
                  .subject(subject)
                  .theory(theoryMarks)
                  .practical(practicalMarks)
                  .total(totalScoredMarks)
                  .grade(grade)
                  .build());
        });
    return scholasticOptional;
  }

  public String getMarks(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    List<LowerGradeReportCardData> data;
    Double average;

    data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();

    if (data.isEmpty()) {
      return null;
    }

    List<LowerGradeReportCardData> nonAttendedData =
        data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!nonAttendedData.isEmpty()) {
      return nonAttendedData.getFirst().getRemarks() == null
          ? "AB"
          : nonAttendedData.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .filter(datas -> datas.getMarks() != null)
            .mapToDouble(datas -> datas.getMarks())
            .average()
            .orElse(0.0);

    return String.format("%.1f", average);
  }

  private String getPtMarks(
      List<String> assessmentSlugs, List<LowerGradeReportCardData> subjectData) {

    List<LowerGradeReportCardData> filteredData =
        subjectData.stream()
            .filter(d -> assessmentSlugs.contains(d.getAssessmentSlug()))
            .filter(d -> d.getMarks() != null && d.getSubjectMarks() != 0)
            .toList();

    if (filteredData.isEmpty()) {
      return "";
    }

    List<LowerGradeReportCardData> sortedData =
        filteredData.stream()
            .sorted(
                (d1, d2) -> {
                  double percentage1 = ((double) d1.getMarks() / d1.getSubjectMarks()) * 100;
                  double percentage2 = ((double) d2.getMarks() / d2.getSubjectMarks()) * 100;
                  return Double.compare(percentage2, percentage1);
                })
            .limit(2)
            .toList();

    double totalMarks = sortedData.stream().mapToDouble(d -> d.getMarks().doubleValue()).sum();

    if (totalMarks == 0) {
      return "-";
    }

    return String.valueOf(Math.floor(totalMarks * 10) / 10.0);
  }

  public Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "8point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String calculateCoScholasticGrade(Double marks) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(marks));
  }

  private List<Scholors3rd8threportDto.CoScholastic> buildCoScholosticOptional(
      List<LowerGradeReportCardData> data) {
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();
    var coScholasticAreaResources = getCoScholasticAreaResources();

    List<Scholors3rd8threportDto.CoScholastic> response = new ArrayList<>();

    List<Scholors3rd8threportDto.Skill> healthSkills =
        buildSkillsForCategory(
            coScholasticOptionalData, coScholasticAreaResources, "disciplinary-traits");
    if (!healthSkills.isEmpty()) {
      response.add(
          Scholors3rd8threportDto.CoScholastic.builder()
              .skillName("DISCIPLINARY TRAITS")
              .skill(healthSkills)
              .build());
    } else {
      return Collections.emptyList();
    }
    return response;
  }

  private List<Scholors3rd8threportDto.CoScholastic> buildCoScholosticMandatory(
      List<LowerGradeReportCardData> data, String gradeSlug) {
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();
    var coScholasticAreaResources = getCoScholasticAreaResources();

    List<Scholors3rd8threportDto.CoScholastic> response = new ArrayList<>();

    List<Scholors3rd8threportDto.Skill> healthSkills =
        buildSkillsForCategory(
            coScholasticOptionalData, coScholasticAreaResources, "health-and-physical-education");
    if (!healthSkills.isEmpty()) {
      response.add(
          Scholors3rd8threportDto.CoScholastic.builder()
              .skillName("HEALTH & PHYSICAL EDUCATION")
              .skill(healthSkills)
              .build());
    }

    List<Scholors3rd8threportDto.Skill> artSkills =
        buildSkillsForCategory(
            coScholasticOptionalData, coScholasticAreaResources, "art-education");
    if (!artSkills.isEmpty()) {
      response.add(
          Scholors3rd8threportDto.CoScholastic.builder()
              .skillName("ART EDUCATION")
              .skill(artSkills)
              .build());
    }

    List<Scholors3rd8threportDto.Skill> knowledgeSkills =
        buildSkillsForCategory(coScholasticOptionalData, coScholasticAreaResources, "gk");
    if (!knowledgeSkills.isEmpty() && !(gradeSlug.equals("ix") || gradeSlug.equals("x"))) {
      response.add(
          Scholors3rd8threportDto.CoScholastic.builder()
              .skillName("GENERAL KNOWLEDGE")
              .skill(knowledgeSkills)
              .build());
    }

    List<Scholors3rd8threportDto.Skill> workExperienceSkills =
        buildSkillsForCategory(
            coScholasticOptionalData, coScholasticAreaResources, "work-experience");
    if (!workExperienceSkills.isEmpty() && (gradeSlug.equals("ix") || gradeSlug.equals("x"))) {
      response.add(
          Scholors3rd8threportDto.CoScholastic.builder()
              .skillName("WORK EXPERIENCE")
              .skill(workExperienceSkills)
              .build());
    }

    if (response.isEmpty()) {
      log.warn("No co-scholastic mandatory data found, creating default entries");
      response.addAll(createDefaultCoScholasticMandatory());
    }

    log.info("Returning {} co-scholastic mandatory entries", response.size());
    return response;
  }

  private List<Scholors3rd8threportDto.Skill> buildSkillsForCategory(
      List<LowerGradeReportCardData> coScholasticData,
      List<InterOverAllReportDto.InterTerm1CoScholastic> coScholasticAreaResources,
      String categoryTitle) {

    return coScholasticData.stream()
        .filter(rcd -> categoryTitle.equals(rcd.getSubjectSlug()))
        .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
        .filter(rcd -> rcd.getMarks() != null || rcd.getRemarks() != null)
        .map(
            rcd -> {
              String grade = calculateCoScholasticGrade(rcd.getMarks());
              if (grade == null || grade.isBlank()) {
                grade =
                    Optional.ofNullable(rcd.getRemarks())
                        .filter(r -> r.length() >= 2)
                        .map(r -> r.substring(0, 2))
                        .orElse("");
              }
              return Scholors3rd8threportDto.Skill.builder()
                  .subject(rcd.getSubjectName())
                  .grade(grade)
                  .build();
            })
        .collect(Collectors.toList());
  }

  private List<Scholors3rd8threportDto.CoScholastic> createDefaultCoScholasticMandatory() {
    List<Scholors3rd8threportDto.CoScholastic> defaultEntries = new ArrayList<>();
    var coScholasticAreaResources = getCoScholasticAreaResources();

    List<Scholors3rd8threportDto.Skill> healthSkills = new ArrayList<>();
    var healthSubjects =
        coScholasticAreaResources.stream()
            .filter(csa -> csa.subjectTitle().equals("health-and-physical-education"))
            .toList();
    healthSubjects.forEach(
        subject -> {
          healthSkills.add(
              Scholors3rd8threportDto.Skill.builder()
                  .subject(subject.subjectName())
                  .grade("")
                  .build());
        });
    if (!healthSkills.isEmpty()) {
      defaultEntries.add(
          Scholors3rd8threportDto.CoScholastic.builder()
              .skillName("HEALTH & PHYSICAL EDUCATION")
              .skill(healthSkills)
              .build());
    }

    List<Scholors3rd8threportDto.Skill> artSkills = new ArrayList<>();
    var artSubjects =
        coScholasticAreaResources.stream()
            .filter(csa -> csa.subjectTitle().equals("art-education"))
            .toList();
    artSubjects.forEach(
        subject -> {
          artSkills.add(
              Scholors3rd8threportDto.Skill.builder()
                  .subject(subject.subjectName())
                  .grade("")
                  .build());
        });
    if (!artSkills.isEmpty()) {
      defaultEntries.add(
          Scholors3rd8threportDto.CoScholastic.builder()
              .skillName("ART EDUCATION")
              .skill(artSkills)
              .build());
    }

    List<Scholors3rd8threportDto.Skill> knowledgeSkills = new ArrayList<>();
    var knowledgeSubjects =
        coScholasticAreaResources.stream()
            .filter(csa -> csa.subjectTitle().equals("general-knowledge"))
            .toList();
    knowledgeSubjects.forEach(
        subject -> {
          knowledgeSkills.add(
              Scholors3rd8threportDto.Skill.builder()
                  .subject(subject.subjectName())
                  .grade("")
                  .build());
        });
    if (!knowledgeSkills.isEmpty()) {
      defaultEntries.add(
          Scholors3rd8threportDto.CoScholastic.builder()
              .skillName("GENERAL KNOWLEDGE")
              .skill(knowledgeSkills)
              .build());
    }

    return defaultEntries;
  }

  private List<InterOverAllReportDto.InterTerm1CoScholastic> getCoScholasticAreaResources() {

    List<InterOverAllReportDto.InterTerm1CoScholastic> coScholasticAreas = new ArrayList<>();
    try {
      var objectMapper = new ObjectMapper();
      coScholasticAreas =
          objectMapper.readValue(ResourceUtils.asString(resource), new TypeReference<>() {});
      if (Objects.isNull(coScholasticAreas) || coScholasticAreas.isEmpty()) {
        return coScholasticAreas;
      }
    } catch (Exception ex) {
      log.error(
          "Unable to process the resource [inter-term1-co-scholastic.json] from the classpath", ex);
      return coScholasticAreas;
    }
    return coScholasticAreas;
  }

  private String buildScholosticTotalPercentage(Scholors3rd8threportDto.Marks marks) {
    if (Objects.isNull(marks)
        || Objects.isNull(marks.marksScored())
        || Objects.isNull(marks.totalMarks())) {
      return null;
    }
    var percentage = (marks.marksScored() / marks.totalMarks()) * 100;
    return String.format("%.1f%%", percentage);
  }

  private String buildScholosticTotalGrade(Scholors3rd8threportDto.Marks marks) {
    if (Objects.isNull(marks)
        || Objects.isNull(marks.marksScored())
        || Objects.isNull(marks.totalMarks())) {
      return null;
    }
    if (marks.marksScored() == 0 || marks.totalMarks() == 0) {
      return "AB";
    }
    return calculateGrade(marks.marksScored(), marks.totalMarks(), "8point");
  }

  private Scholors3rd8threportDto.Marks getMarksResponse(
      List<Scholors3rd8threportDto.ScholasticMandatory> scholasticMandatory,
      List<Scholors3rd8threportDto.ScholasticOptional> scholasticOptional) {
    if (Objects.isNull(scholasticMandatory) || scholasticMandatory.isEmpty()) {
      return null;
    }
    var mandatoryTotal =
        scholasticMandatory.stream()
            .mapToDouble(sm -> sumMarks(sm.portfolio(), sm.se(), sm.hye(), sm.pt(), sm.ma()))
            .sum();

    Scholors3rd8threportDto.Marks marks =
        Scholors3rd8threportDto.Marks.builder()
            .marksScored(mandatoryTotal)
            .totalMarks((double) (scholasticMandatory.size() * 100))
            .build();

    return marks;
  }

  private List<LowerGradeReportCardData> getData(Student student) {
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    return data;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("scholors-3rd-8th-report-card.xml");
  }

  public double attendance(String orgSlug, Long studentId) {
    return sectionAttendanceDetailRepository.getAttendancePercentageByStudent(orgSlug, studentId);
  }
}
