package com.wexl.gilico.controller;

import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/gillco-progress-card")
@RequiredArgsConstructor
public class GilicoReportCardController {

  private final ProgressCardService gillcoReportCardService;

  @PostMapping()
  public ProgressCardDto.ProgressCardResponse saveProgressCard(
      @PathVariable("orgSlug") String orgSlug, @RequestBody ProgressCardDto.Request request) {
    return gillcoReportCardService.saveProgressCard(orgSlug, request);
  }

  @GetMapping("/students/{studentAuthUserId}")
  public ProgressCardDto.Response getProgressCardById(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("studentAuthUserId") String studentAuthUserId) {
    return gillcoReportCardService.getProgressCardById(orgSlug, studentAuthUserId);
  }
}
