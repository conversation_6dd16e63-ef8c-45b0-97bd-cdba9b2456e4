package com.wexl.scholars.dto;

import java.util.List;
import lombok.Builder;

public record ScholarsReportCardDto() {

  @Builder
  public record Header(
      String schoolName, String academicYear, String testName, String className, String section) {}

  @Builder
  public record Body(
      String subject,
      Long totalMarks,
      FirstTable firstTable,
      String teacherName,
      String reCheckerName,
      String signOfPrincipal,
      String signOfInCharge,
      String signOfHOD,
      String subTeacher,
      String failed,
      String studentAppeared,
      String range,
      String mean) {}

  @Builder
  public record FirstTable(
      String column1,
      String column2,
      String column3,
      String column4,
      String column5,
      String column6,
      String total,
      List<StudentMarks> marks) {}

  @Builder
  public record StudentMarks(
      String sno,
      String admissionNo,
      String name,
      String sec1,
      String sec2,
      String sec3,
      String sec4,
      String sec5,
      String sec6,
      String total) {}
}
