package com.wexl.erp.fees.service.fineType;

import com.wexl.erp.fees.model.FeeGroupFeeType;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FineType;
import java.time.LocalDateTime;
import org.springframework.stereotype.Component;

@Component
public class PercentageRule implements FineTypeRule {

  @Override
  public boolean supports(FeeGroupFeeType feeHeadFeeType) {
    return FineType.PERCENTAGE.equals(feeHeadFeeType.getFineType());
  }

  @Override
  public Double calculateFine(FeeHead feeHead, FeeGroupFeeType feeGroupFeeType) {
    LocalDateTime dueDate = feeHead.getDueDate();
    Double fineAmount = feeHead.getFineAmount() != null ? feeHead.getFineAmount() : 0.0;

    if (dueDate != null && dueDate.isBefore(LocalDateTime.now())) {
      return fineAmount;
    }
    return 0.0;
  }
}
