package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.*;
import com.wexl.erp.fees.repository.ConcessionHeadRepository;
import com.wexl.erp.fees.repository.ConcessionRepository;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.util.ValidationUtils;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FeeConcessionService {

  private final ConcessionRepository concessionRepository;
  private final ConcessionHeadRepository concessionHeadRepository;
  private final FeeHeadRepository feeHeadRepository;
  private final AuthService authService;
  private final FeeService feeService;
  private final FeeHeadService feeHeadService;
  private final ValidationUtils validationUtils;

  public void saveConcessions(String orgSlug, FeeDto.ConcessionRequest request) {
    if (request.concessionType() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Concession.TypeRequired");
    }
    if (request.value() == null || request.value() <= 0) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Concession.ValueRequired");
    }
    concessionRepository.save(
        Concession.builder()
            .orgSlug(orgSlug)
            .type(request.concessionType())
            .value(request.value())
            .description(request.description())
            .createdBy(authService.getUserDetails())
            .build());
  }

  public List<FeeDto.ConcessionResponse> getConcessions(String orgSlug) {
    List<FeeDto.ConcessionResponse> responses = new ArrayList<>();
    var concessions = concessionRepository.findAllByOrgSlugOrderByCreatedAtDesc(orgSlug);
    if (concessions.isEmpty()) {
      return responses;
    }
    concessions.forEach(
        concession ->
            responses.add(
                FeeDto.ConcessionResponse.builder()
                    .id(concession.getId())
                    .concessionType(concession.getType())
                    .value(concession.getValue())
                    .description(concession.getDescription())
                    .createdOn(convertIso8601ToEpoch(concession.getCreatedAt().toLocalDateTime()))
                    .isPublished(concession.getPublishedAt() != null ? Boolean.TRUE : Boolean.FALSE)
                    .createdBy(
                        concession.getCreatedBy().getFirstName()
                            + " "
                            + concession.getCreatedBy().getLastName())
                    .build()));
    return responses;
  }

  public void updateConcessionById(
      String orgSlug, String concessionId, FeeDto.ConcessionRequest request) {
    var concession = getConcessionById(concessionId, orgSlug);
    concession.setType(request.concessionType());
    concession.setValue(request.value());
    concession.setDescription(request.description());
    concessionRepository.save(concession);
  }

  public void deleteConcessionById(String orgSlug, String concessionId) {
    var concession = getConcessionById(concessionId, orgSlug);
    concessionRepository.delete(concession);
  }

  public void saveConcessionHeads(String orgSlug, FeeDto.ConcessionHeadRequest request) {

    if (request.feeHeadId() == null || request.studentId() == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.ConcessionHead.InvalidRequest");
    }
    var feeHead = getFeeHeadById(request.feeHeadId(), orgSlug);
    for (String concessionId : request.ConcessionId()) {
      var concession = getConcessionById(concessionId, orgSlug);
      var discountAmount = feeHeadService.getDiscountAmount(concession, feeHead);

      var concessionHead =
          ConcessionHead.builder()
              .feeType(feeHead.getFeeType())
              .concession(concession)
              .createdBy(authService.getUserDetails())
              .student(feeHead.getStudent())
              .isApproved(Boolean.FALSE)
              .feeHead(feeHead)
              .build();

      concessionHeadRepository.save(concessionHead);

      feeHead.setBalanceAmount(feeHead.getBalanceAmount() - discountAmount);
      feeHead.setDiscountAmount(feeHead.getDiscountAmount() + discountAmount);
    }
    feeHeadRepository.save(feeHead);
  }

  private Concession getConcessionById(String concessionId, String orgSlug) {
    var concession =
        concessionRepository.findByIdAndOrgSlug(UUID.fromString(concessionId), orgSlug);
    if (concession.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Concession.NotFound");
    }
    return concession.get();
  }

  private FeeHead getFeeHeadById(String feeHeadId, String orgSlug) {
    var feeHead = feeHeadRepository.findByIdAndOrgSlug(UUID.fromString(feeHeadId), orgSlug);
    if (feeHead.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.FeeHead.NotFound");
    }
    return feeHead.get();
  }

  public List<FeeDto.ConcessionHeadResponse> getConcessionHeads(
      String orgSlug, String concessionId) {
    var concession = getConcessionById(concessionId, orgSlug);
    var feeHeads = feeHeadRepository.findAllByConcession(concession);

    if (feeHeads.isEmpty()) {
      return List.of();
    }

    return feeHeads.stream().map(this::mapToConcessionHeadResponse).toList();
  }

  private FeeDto.ConcessionHeadResponse mapToConcessionHeadResponse(FeeHead feeHead) {
    var student = feeHead.getStudent();
    var userInfo = student.getUserInfo();
    var feeType = feeHead.getFeeType();
    var feeMaster = feeHead.getFeeMaster();
    var feeGroup = feeMaster.getFeeGroup();

    return FeeDto.ConcessionHeadResponse.builder()
        .feeHeadId(feeType.getId().toString())
        .studentId(student.getId())
        .studentName(userInfo.getFirstName() + " " + userInfo.getLastName())
        .feeTypeCode(feeType.getCode())
        .feeTypeName(feeType.getName())
        .feeTypeDescription(feeType.getDescription())
        .feeMasterId(feeMaster.getId().toString())
        .feeGroupId(feeGroup.getId().toString())
        .feeGroupName(feeGroup.getName())
        .feeGroupDescription(feeGroup.getDescription())
        .build();
  }

  private ConcessionHead getConcessionHeadById(
      String concessionHeadId, String orgSlug, Concession concession) {
    var concessionHead = concessionHeadRepository.findById(UUID.fromString(concessionHeadId));
    if (concessionHead.isEmpty() || !concessionHead.get().getConcession().equals(concession)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ConcessionHead.NotFound");
    }
    return concessionHead.get();
  }

  public void deleteConcessionHeadById(
      String orgSlug, String concessionId, String concessionHeadId) {
    var concession = getConcessionById(concessionId, orgSlug);
    var concessionHead = getConcessionHeadById(concessionHeadId, orgSlug, concession);
    if (Boolean.TRUE.equals(concessionHead.getIsApproved())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.ConcessionHead.AlreadyApproved");
    }
    concessionHeadRepository.delete(concessionHead);
  }

  public void publishConcession(String orgSlug, String concessionId) {
    var concession = getConcessionById(concessionId, orgSlug);
    concession.setPublishedAt(LocalDateTime.now());
    concessionRepository.save(concession);
  }

  public void assignConcessions(
      String orgSlug, FeeDto.AssignConcessionRequest request, String concessionId) {
    var concession = getConcessionById(concessionId, orgSlug);
    var feeMaster = feeService.getFeeMasterById(request.feeMasterId(), orgSlug);
    var feeType = feeService.getFeeTypeById(request.feeTypeId(), orgSlug);
    var feeHeads = feeHeadRepository.findAllByFeeMasterAndOrgSlug(feeMaster, orgSlug);
    var feeGroup = feeService.getFeeGroupById(request.feeGroupId(), orgSlug);

    request
        .students()
        .forEach(
            studentId -> {
              var studentFeeHeadOpt = findStudentFeeHead(feeType, feeHeads, studentId);

              if (studentFeeHeadOpt.isPresent()) {
                applyConcession(studentFeeHeadOpt.get(), concession, studentId, feeType);
              } else {
                createFeeHeadForStudent(
                    studentId, feeMaster, feeType, orgSlug, feeGroup, concession);
              }
            });
  }

  private Optional<FeeHead> findStudentFeeHead(
      FeeType feeType, List<FeeHead> feeHeads, Long studentId) {
    return feeHeads.stream()
        .filter(
            feeHead ->
                studentId.equals(feeHead.getStudent().getId())
                    && feeHead.getFeeType().getId().equals(feeType.getId()))
        .findFirst();
  }

  private void applyConcession(
      FeeHead feeHead, Concession concession, Long studentId, FeeType feeType) {
    var student = validationUtils.isStudentValid(studentId);

    var concessionHead =
        ConcessionHead.builder()
            .feeType(feeType)
            .concession(concession)
            .createdBy(authService.getUserDetails())
            .student(student)
            .isApproved(Boolean.FALSE)
            .feeHead(feeHead)
            .build();
    concessionHeadRepository.save(concessionHead);

    var discountAmount = feeHeadService.getDiscountAmount(concession, feeHead);
    var feeHeadDiscountAmount =
        feeHead.getDiscountAmount() != null ? feeHead.getDiscountAmount() : 0.0;
    feeHead.setDiscountAmount(feeHeadDiscountAmount + discountAmount);
    feeHead.setConcession(concession);
    feeHeadRepository.save(feeHead);
  }

  private void createFeeHeadForStudent(
      Long studentId,
      FeeMaster feeMaster,
      FeeType feeType,
      String orgSlug,
      FeeGroup feeGroup,
      Concession concession) {
    var student = validationUtils.isStudentValid(studentId);
    var feeHead =
        feeHeadService.saveFeeHead(student, feeMaster, feeType, orgSlug, feeGroup, concession);
    var concessionHead =
        ConcessionHead.builder()
            .feeType(feeType)
            .concession(concession)
            .createdBy(authService.getUserDetails())
            .student(student)
            .isApproved(Boolean.FALSE)
            .feeHead(feeHead)
            .build();
    concessionHeadRepository.save(concessionHead);
  }
}
