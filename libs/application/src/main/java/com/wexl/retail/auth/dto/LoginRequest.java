package com.wexl.retail.auth.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class LoginRequest {
  @NotNull private String username;

  @NotNull private String password;
  @NotNull private String appContext;

  @JsonProperty("firebase_token")
  private String firebaseToken;
}
