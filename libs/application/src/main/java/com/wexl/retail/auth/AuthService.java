package com.wexl.retail.auth;

import static com.wexl.retail.commons.util.JwtConstants.*;
import static com.wexl.retail.metrics.MetricConstants.ORG;
import static com.wexl.retail.metrics.MetricConstants.SECTION;
import static com.wexl.retail.util.AccessToken.TEMP_USER_NAME;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import com.wexl.retail.auth.dto.LoginRequest;
import com.wexl.retail.auth.dto.LoginResponse;
import com.wexl.retail.auth.dto.MobileNumberLoginDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.utils.JwtAuthentication;
import com.wexl.retail.commons.security.utils.JwtIdTokenCredentialsHolder;
import com.wexl.retail.commons.util.CastUtil;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.commons.util.JwtConstants;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.device.service.DeviceService;
import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.model.LoginDevice;
import com.wexl.retail.model.LoginHistory;
import com.wexl.retail.model.LoginMethod;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.TeacherMetadata;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserRole;
import com.wexl.retail.model.UserVerificationStatus;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.otp.Otp;
import com.wexl.retail.otp.OtpRepository;
import com.wexl.retail.repository.LoginHistoryRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.staff.repository.StaffRepository;
import com.wexl.retail.student.subject.profiles.dto.SubjectProfileOrgs;
import com.wexl.retail.teacher.orgs.TeacherOrgsRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.MobileAppUtil;
import com.wexl.retail.util.Tuple;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import javax.crypto.spec.SecretKeySpec;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

  private final UserRepository userRepository;
  private final UserIdpService userIdpService;
  private final TeacherOrgsRepository teacherOrgsRepository;
  private final LoginHistoryRepository loginHistoryRepository;
  private final OrganizationRepository organizationRepository;
  private final UserRoleHelper userRoleHelper;
  private final OtpRepository otpRepository;
  private final SubjectProfileOrgs subjectProfileOrgs;
  private final DeviceService deviceService;
  private final ContentService contentService;
  private final StudentRepository studentRepository;
  private final StaffRepository staffRepository;
  private static final Random random = new Random();

  @Value("${app.device.single.organizations}")
  private String[] singleDeviceOrgs;

  @Value("${app.device.switch.organizations}")
  private String[] switchDeviceOrgs;

  @Value("${jwt.tokenSecret}")
  private String tokenSecret;

  @Value("${jwt.validityInDays:60}")
  private int validityInDays;

  @Value("${spring.profiles.active:default}")
  private String envProfile;

  @Value("${app.mobileNumberLogin.users.limit}")
  private Integer mobileNumberLoginUsersLimit;

  @Value("${jwt.erpTokenSecret}")
  private String erpTokenSecret;

  private static final String PARSE_TOKEN = "error.ParseToken";

  public User getUserByAuthUserId(String authUserId) {
    return userRepository.getUserByAuthUserId(authUserId);
  }

  public User getUserByUserName(String userName) {
    if (userName == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.WrongCredentials");
    }
    return userRepository.getUserByUserName(userName);
  }

  private Tuple<User, Claims> getUserInfo() {
    var authentication = SecurityContextHolder.getContext().getAuthentication();
    var claims = ((JwtAuthentication) authentication).getJwtClaimsSet();
    var userDetails = new User();
    userDetails.setAuthUserId(claims.getSubject());
    userDetails.setId((int) claims.get(ID));
    userDetails.setFirstName((String) claims.get(FIRST_NAME));
    userDetails.setLastName((String) claims.get(LAST_NAME));
    userDetails.setEmail((String) claims.get(EMAIL));
    userDetails.setOrganization((String) claims.get(ORGANIZATION));
    userDetails.setGuid((String) claims.get(GUID));
    return new Tuple<>(userDetails, claims);
  }

  public User getUserDetails() {
    return getUserInfo().getUserDetails();
  }

  public User getStudentDetails() {
    var claims = getUserInfo().getClaims();
    var student = new Student();
    student.setId((int) claims.get(STUDENT_ID));
    student.setSchoolName((String) claims.get(SCHOOL_NAME));
    student.setClassId((int) claims.get(CLASS_ID));
    student.setBoardId((int) claims.get(BOARD_ID));
    var userDetails = getUserInfo().getUserDetails();
    userDetails.setStudentInfo(student);
    return userDetails;
  }

  public User getTeacherDetails() {
    var claims = getUserInfo().getClaims();
    var userDetails = getUserInfo().getUserDetails();
    userDetails.setMobileNumber((String) claims.get(MOBILE_NUMBER));
    var teacherDetails = new Teacher();
    teacherDetails.setId((int) claims.get(TEACHER_DETAILS_ID));
    userDetails.setTeacherInfo(teacherDetails);
    userDetails.setVerificationStatus(
        UserVerificationStatus.valueOf(claims.get(VERIFICATION_STATUS).toString()));
    var subjects = CastUtil.castList(claims.get(SUBJECTS), String.class);
    var teacherMetadata = TeacherMetadata.builder().subjects(subjects).build();
    teacherDetails.setMetadata(teacherMetadata);
    return userDetails;
  }

  private JwtBuilder getUserJwtBuilder(
      Boolean requestComingFromMobileApp, User user, LoginMethod loginMethod) {
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    var currentDate = Instant.now();
    var permissions =
        Objects.equals(user.getAuthUserId(), TEMP_USER_NAME) ? "" : concatUserPermissions(user);
    var jwtBuilder =
        Jwts.builder()
            .claim(ID, user.getId())
            .claim(FIRST_NAME, user.getFirstName())
            .claim(LAST_NAME, user.getLastName())
            .claim(EMAIL, user.getEmail())
            .claim(ORGANIZATION, user.getOrganization())
            .claim(SCOPE, permissions)
            .claim(IS_MOBILE, requestComingFromMobileApp)
            .claim(LOGIN_METHOD, loginMethod.toString())
            .setSubject(user.getAuthUserId())
            .setId(String.valueOf(user.getId()))
            .setIssuedAt(Date.from(currentDate))
            .setExpiration(Date.from(currentDate.plus(validityInDays, ChronoUnit.DAYS)))
            .signWith(tokenSignInKey, SignatureAlgorithm.HS256);

    if (Objects.nonNull(user.getGuid())) {
      jwtBuilder.claim(GUID, user.getGuid());
    }

    return jwtBuilder;
  }

  private JwtBuilder getStaffUserJwtBuilder(
      Boolean requestComingFromMobileApp, User user, LoginMethod loginMethod) {
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    var currentDate = Instant.now();
    var permissions =
        Objects.equals(user.getAuthUserId(), TEMP_USER_NAME) ? "" : concatUserPermissions(user);
    var staff = staffRepository.findByUser(user);
    var roleTemplates = staff.get().getRole();
    List<String> apps = new ArrayList<>();
    roleTemplates.forEach(role -> apps.add(role.getTemplate().toString()));
    var jwtBuilder =
        Jwts.builder()
            .claim(ID, user.getId())
            .claim(FIRST_NAME, user.getFirstName())
            .claim(LAST_NAME, user.getLastName())
            .claim(EMAIL, user.getEmail())
            .claim(ORGANIZATION, user.getOrganization())
            .claim(SCOPE, permissions)
            .claim(IS_MOBILE, requestComingFromMobileApp)
            .claim(LOGIN_METHOD, loginMethod.toString())
            .claim(APPS, apps)
            .setSubject(user.getAuthUserId())
            .setId(String.valueOf(user.getId()))
            .setIssuedAt(Date.from(currentDate))
            .setExpiration(Date.from(currentDate.plus(validityInDays, ChronoUnit.DAYS)))
            .signWith(tokenSignInKey, SignatureAlgorithm.HS256);

    if (Objects.nonNull(user.getGuid())) {
      jwtBuilder.claim(GUID, user.getGuid());
    }

    return jwtBuilder;
  }

  private JwtBuilder getErpToken(User user) {
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(erpTokenSecret), SignatureAlgorithm.HS256.getJcaName());
    var currentDate = Instant.now();
    return Jwts.builder()
        .claim(ID, user.getId())
        .claim(USER_NAME, user.getUserName())
        .claim(FIRST_NAME, user.getFirstName())
        .claim(LAST_NAME, user.getLastName())
        .claim(EMAIL, user.getEmail())
        .claim(ORGANIZATION, user.getOrganization())
        .setIssuedAt(Date.from(currentDate))
        .setExpiration(Date.from(currentDate.plus(validityInDays, ChronoUnit.DAYS)))
        .signWith(tokenSignInKey, SignatureAlgorithm.HS256);
  }

  private String concatUserPermissions(User user) {
    return String.join(" ", evaluationPermissionsForUser(user));
  }

  public String generateAccessToken(
      Boolean requestComingFromMobileApp, User user, LoginMethod loginMethod) {
    try {
      return getUserJwtBuilder(requestComingFromMobileApp, user, loginMethod)
          .claim(MOBILE_NUMBER, user.getMobileNumber())
          .claim(ADDRESS, user.getAddresses())
          .claim(ROLES, userRoleHelper.getUserRolesFromUser(user))
          .compact();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ProcessingAuth", e);
    }
  }

  public String generateErpToken(User user) {
    if (user.getStudentInfo() != null) {
      var student = user.getStudentInfo();
      return getErpToken(user)
          .claim(SCHOOL_NAME, student.getSchoolName())
          .claim(SECTION, student.getSection().getId())
          .claim(GRADE, student.getSection().getGradeName())
          .claim(CLASS_ID, student.getClassId())
          .claim(STUDENT_ID, student.getId())
          .claim(BOARD_NAME, student.getSection().getBoardName())
          .claim(MOBILE_NUMBER, user.getMobileNumber())
          .compact();
    } else if (user.getTeacherInfo() != null) {
      var teacher = user.getTeacherInfo();
      return getErpToken(user)
          .claim(MOBILE_NUMBER, user.getMobileNumber())
          .claim(TEACHER_DETAILS_ID, teacher.getId())
          .claim(SECTIONS, getSectionList(user))
          .compact();
    }
    return null;
  }

  public String generate(
      Boolean requestComingFromMobileApp, User teacher, LoginMethod loginMethod) {
    try {
      return getUserJwtBuilder(requestComingFromMobileApp, teacher, loginMethod)
          .claim(MOBILE_NUMBER, teacher.getMobileNumber())
          .claim(TEACHER_DETAILS_ID, teacher.getTeacherInfo().getId())
          .claim(VERIFICATION_STATUS, teacher.getVerificationStatus())
          .claim(ROLES, userRoleHelper.getUserRolesFromUser(teacher))
          .claim(SECTIONS, getSectionList(teacher))
          .claim(SUBJECTS, teacher.getTeacherInfo().getMetadata().getSubjects())
          .compact();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExpiredOrInvalidToken", e);
    }
  }

  public String generateStudentAccessToken(
      Boolean requestComingFromMobileApp, User student, LoginMethod loginMethod) {
    var studentData =
        studentRepository
            .findByUserInfo(student)
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentFind.User"));
    try {
      var grade = contentService.getGradeById(studentData.getClassId()).getName();
      return getUserJwtBuilder(requestComingFromMobileApp, student, loginMethod)
          .claim(SCHOOL_NAME, studentData.getSchoolName())
          .claim(GRADE, grade)
          .claim(CLASS_ID, studentData.getClassId())
          .claim(STUDENT_ID, studentData.getId())
          .claim(BOARD_ID, studentData.getBoardId())
          .claim(IS_PREMIUM, true)
          .claim(ROLES, userRoleHelper.getUserRolesFromUser(student))
          .claim(MOBILE_NUMBER, student.getMobileNumber())
          .compact();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExpiredOrInvalidToken", e);
    }
  }

  public String generateStaffAccessToken(
      Boolean requestComingFromMobileApp, User staff, LoginMethod loginMethod) {
    var staffData =
        staffRepository
            .findByUser(staff)
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentFind.User"));
    try {
      return getStaffUserJwtBuilder(requestComingFromMobileApp, staff, loginMethod)
          .claim(ROLES, userRoleHelper.getUserRolesFromUser(staff).stream().distinct().toList())
          .compact();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExpiredOrInvalidToken", e);
    }
  }

  public String generateTeacherAccessToken(
      Boolean requestComingFromMobileApp, User teacher, LoginMethod loginMethod) {
    try {
      return getUserJwtBuilder(requestComingFromMobileApp, teacher, loginMethod)
          .claim(MOBILE_NUMBER, teacher.getMobileNumber())
          .claim(TEACHER_DETAILS_ID, teacher.getTeacherInfo().getId())
          .claim(VERIFICATION_STATUS, teacher.getVerificationStatus())
          .claim(ROLES, userRoleHelper.getUserRolesFromUser(teacher))
          .claim(SECTIONS, getSectionList(teacher))
          .claim(SUBJECTS, teacher.getTeacherInfo().getMetadata().getSubjects())
          .compact();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExpiredOrInvalidToken", e);
    }
  }

  private List<UUID> getSectionList(User teacher) {
    if (AuthUtil.isOrgAdmin(teacher)) {
      // just retrieve the first section for orgAdmin to reduce the size of JWT token
      final Optional<UUID> possibleSection =
          teacher.getTeacherInfo().getSections().stream().map(Section::getUuid).findFirst();
      return possibleSection.map(List::of).orElseGet(Collections::emptyList);
    }

    return teacher.getTeacherInfo().getSections().stream().map(Section::getUuid).toList();
  }

  public String generateOrgAdminAccessToken(
      Boolean requestComingFromMobileApp, User user, LoginMethod loginMethod) {
    return getUserJwtBuilder(requestComingFromMobileApp, user, loginMethod)
        .claim(EMAIL, user.getEmail())
        .claim(ORGANIZATION, user.getOrganization())
        .claim(MOBILE_NUMBER, user.getMobileNumber())
        .claim(VERIFICATION_STATUS, user.getVerificationStatus())
        .claim(ROLES, userRoleHelper.getUserRolesFromUser(user))
        .claim(SCOPE, concatUserPermissions(user))
        .compact();
  }

  public String generateAdminAccessToken(User user, LoginMethod loginMethod) {
    return getUserJwtBuilder(false, user, loginMethod)
        .claim(ROLES, List.of(UserRole.ROLE_ORG_ADMIN, UserRole.ROLE_ADMIN, UserRole.ROLE_ITEACHER))
        .claim(SUBJECTS, "")
        .compact();
  }

  public boolean isUserEmailExist(String email) {
    List<User> userList = userRepository.findByEmailWithIgnoreCase(email);
    return !userList.isEmpty();
  }

  public User findFirstMatchingEmail(String email) {
    // TODO: This is a temporary hacky code while fixing another bug
    // TODO: Will return to this later as a separate issue
    List<User> userList = userRepository.findByEmailWithIgnoreCase(email);
    if (userList.isEmpty()) {
      return null;
    }
    return userList.getFirst();
  }

  public boolean isUserDisabled() {
    var refreshedUser = getUserByAuthUserId(getUserDetails().getAuthUserId());
    return refreshedUser != null && refreshedUser.getDeletedAt() != null;
  }

  public LoginResponse signin(
      boolean requestComingFromMobileApp, LoginRequest loginRequest, LoginMethod loginMethod) {
    try {
      var jwt =
          userIdpService.loginUser(massage(loginRequest.getUsername()), loginRequest.getPassword());
      var user = retrieveUserFromToken(jwt);

      verifyUserLogin(loginRequest.getUsername(), user);
      saveLoginHistory(user, LoginMethod.USERNAME_PASSWORD, LoginDevice.MOBILE);
      user = updateUserLastLoginGuidAndFirebaseToken(loginRequest, user);
      /*  deviceService.updateGuidWhileMobileNumberLogin(loginRequest.getAppContext(), user);*/
      return getUserAccessToken(requestComingFromMobileApp, user, loginMethod);
    } catch (Exception e) {
      log.info("Error while login: " + e.getMessage());
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED,
          "error.UserLogin.UserName",
          new String[] {loginRequest.getUsername()},
          e);
    }
  }

  public LoginResponse signInWithEmail(User user) {
    try {
      verifyUserLogin("", user);
      saveLoginHistory(user, LoginMethod.GOOGLE, LoginDevice.WEB);
      return getUserAccessToken(false, user, LoginMethod.GOOGLE);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED,
          "error.UserLogin.UserName",
          new String[] {user.getAuthUserId()},
          e);
    }
  }

  private String massage(String username) {
    var trimmedUserName = username.trim();
    return trimmedUserName.toLowerCase(Locale.US);
  }

  public LoginResponse getUserAccessToken(
      Boolean requestComingFromMobileApp, User user, LoginMethod loginMethod) {

    String role = "STUDENT";
    String token = "";
    String erpToken = generateErpToken(user);
    if (AuthUtil.isStudent(user)) {
      role = "STUDENT";
      token = generateStudentAccessToken(requestComingFromMobileApp, user, loginMethod);
    } else if (AuthUtil.isTeacher(user)) {
      role = "TEACHER";
      token = generateTeacherAccessToken(requestComingFromMobileApp, user, loginMethod);
    } else if (AuthUtil.isStaff(user)) {
      role = "STAFF";
      token = generateStaffAccessToken(requestComingFromMobileApp, user, loginMethod);
    }

    return LoginResponse.builder()
        .jwtToken(token)
        .role(role)
        .permissions(evaluationPermissionsForUser(user))
        .erpToken(erpToken)
        .build();
  }

  private User updateUserLastLoginGuidAndFirebaseToken(LoginRequest loginRequest, User user) {
    user.setLastLogin(Date.from(Instant.now()));
    user.setGuid(loginRequest.getAppContext());
    user.setFirebaseToken(loginRequest.getFirebaseToken());
    return userRepository.save(user);
  }

  private void verifyUserLogin(String userName, User user) {
    if (user == null) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED,
          "error.UnableToLoginUserWithUserName",
          new String[] {userName});
    }

    if (user.getDeletedAt() != null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.User.Disable");
    }
  }

  public User retrieveUserFromToken(String tokenString) {
    var username = userIdpService.retrieveUsernameFromIdpToken(tokenString);
    var user = getUserByAuthUserId(username);

    if (user == null) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED, "error.AuthUserID.Exist", new String[] {username});
    }
    return user;
  }

  public void validateDetails(final User user) {
    try {
      if (AuthUtil.isStudent(user)) {
        validateVerificationStatus(user);
      }

      if (AuthUtil.isTeacher(user)) {
        validateEmailVerified(user);
      }
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExpiredOrInvalidToken", e);
    }
  }

  public void validateVerificationStatus(User user) {
    if (!user.getVerificationStatus().equals(UserVerificationStatus.VERIFIED)) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED,
          "error.UserName.Status",
          new String[] {user.getUserName()});
    }
  }

  public void validateEmailVerified(User user) {
    if (!Boolean.TRUE.equals(user.getEmailVerified())) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED,
          "error.InvalidUserEmail",
          new String[] {user.getEmail()});
    }
  }

  public void updateUserLastLoginAndGuid(User user, String guid) {
    try {
      var currentDate = Date.from(Instant.now());
      user.setLastLogin(currentDate);
      user.setGuid(guid);
      saveLoginHistory(user, LoginMethod.USERNAME_PASSWORD, LoginDevice.WEB);
      userRepository.updateUserLastLoginAndGuid(currentDate, guid, user.getAuthUserId());
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ProcessingAuth", e);
    }
  }

  public void validateDeviceDetails(User user, String guid) {

    if (ObjectUtils.isEmpty(user)
        || ObjectUtils.isEmpty(user.getGuid())
        || user.getGuid().equals(guid)) {
      return;
    }

    if (AuthUtil.isTeacher(user)) {
      return;
    }

    if (!shouldUseSingleDevice(user.getOrganization()) || canSwitchDevice(user.getOrganization())) {
      return;
    }

    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentCombination.Device");
  }

  private boolean shouldUseSingleDevice(String organization) {
    return Arrays.asList(singleDeviceOrgs).contains(organization);
  }

  private boolean canSwitchDevice(String organization) {
    return Arrays.asList(switchDeviceOrgs).contains(organization);
  }

  public boolean isTeacherAssociatedOrg(String childOrgSlug, User user) {
    // For purpose of my-dashboard in wexl-internal, allowing all metrics and my-organization apis
    // without any authentication
    if (user.getOrganization().equals(Constants.WEXL_INTERNAL)
        && userRoleHelper.getUserRolesFromUser(user).contains(UserRole.ROLE_ORG_ADMIN)) {
      return true;
    }
    // TODO: Removed the user role Manager here.  need to fix if there are any issues!
    return teacherOrgsRepository.isTeacherAssociatedOrg(childOrgSlug, user.getId());
  }

  public void saveLoginHistory(User user, LoginMethod loginMethod, LoginDevice loginDevice) {
    try {
      LoginHistory loginHistory =
          LoginHistory.builder()
              .userId(user.getId())
              .loginTime(new Date())
              .loginDevice(loginDevice)
              .loginMethod(loginMethod)
              .build();
      loginHistoryRepository.save(loginHistory);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExpiredOrInvalidToken", e);
    }
  }

  public String validateMobileNumber(MobileNumberLoginDto.MobileNumberLoginOtpRequest request) {
    PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();

    Phonenumber.PhoneNumber phoneNumber;
    try {
      phoneNumber = phoneNumberUtil.parse(request.mobileNumber(), request.countryCode());
    } catch (NumberParseException e) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "error.ParsePhoneNumber",
          new String[] {request.mobileNumber()});
    }

    if (!phoneNumberUtil.isValidNumber(phoneNumber)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.PhoneNumber",
          new String[] {phoneNumber.getRawInput()});
    }
    return phoneNumberUtil.format(phoneNumber, PhoneNumberUtil.PhoneNumberFormat.E164);
  }

  public MobileNumberLoginDto.MobileNumberLoginResponse signInWithMobileNumber(
      String mobileNumber,
      MobileNumberLoginDto.MobileNumberLoginRequest mobileNumberLoginRequest,
      String userAgent) {
    List<User> optionalUsers = userRepository.findUsersByMobileNumber(mobileNumber);
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    var jwtBuilder =
        Jwts.builder()
            .claim(MOBILE_NUMBER, mobileNumber)
            .setIssuedAt(Date.from(Instant.now()))
            .setExpiration(
                Date.from(
                    Date.from(Instant.now()).toInstant().plus(validityInDays, ChronoUnit.DAYS)))
            .signWith(SignatureAlgorithm.HS256, tokenSignInKey);
    if (optionalUsers.isEmpty()) {

      return MobileNumberLoginDto.MobileNumberLoginResponse.builder()
          .linkJwtToken(jwtBuilder.compact())
          .users(Collections.emptyList())
          .userStatus("failed")
          .build();
    }
    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);

    List<LoginResponse> usersLoginResponses =
        buildLoginResponseList(
            optionalUsers,
            mobileNumberLoginRequest.appContext(),
            mobileNumberLoginRequest.firebaseToken(),
            requestComingFromMobileApp,
            mobileNumber);
    return MobileNumberLoginDto.MobileNumberLoginResponse.builder()
        .linkJwtToken(jwtBuilder.compact())
        .userStatus("success")
        .users(usersLoginResponses)
        .build();
  }

  private List<LoginResponse> buildLoginResponseList(
      List<User> optionalUsers,
      String appContext,
      String firebaseToken,
      boolean isRequestFromMobileApp,
      String mobileNumber) {
    List<LoginResponse> usersLoginResponses = new ArrayList<>();
    optionalUsers.forEach(
        user -> {
          try {
            validateDeviceDetails(user, appContext);
            verifyUserLogin(user.getUserName(), user);
            saveLoginHistory(
                user,
                LoginMethod.MOBILE_NUMBER,
                Boolean.TRUE.equals(isRequestFromMobileApp) ? LoginDevice.MOBILE : LoginDevice.WEB);
            if (Objects.nonNull(appContext) && !Strings.isBlank(appContext)) {
              /* deviceService.updateGuidWhileMobileNumberLogin(appContext, user);*/
              user =
                  updateUserLastLoginGuidAndFirebaseToken(
                      LoginRequest.builder()
                          .appContext(appContext)
                          .firebaseToken(firebaseToken)
                          .build(),
                      user);
            }
            usersLoginResponses.add(
                getUserAccessToken(isRequestFromMobileApp, user, LoginMethod.MOBILE_NUMBER));
          } catch (Exception e) {
            throw new ApiException(
                InternalErrorCodes.UN_AUTHORIZED,
                "error.MobileNumber.Login",
                new String[] {mobileNumber},
                true);
          }
        });
    return usersLoginResponses;
  }

  public MobileNumberLoginDto.MobileNumberLoginResponse validateOtp(
      MobileNumberLoginDto.MobileNumberLoginRequest mobileNumberLoginRequest, String userAgent) {

    Optional<Otp> possibleOtp = otpRepository.findFirstById(mobileNumberLoginRequest.otpId());

    if (possibleOtp.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OTP");
    }

    String mobileNumber = possibleOtp.get().getTarget();

    if (List.of(Constants.DEV_PROFILE, Constants.LOCAL_PROFILE).contains(envProfile)) {
      return signInWithMobileNumber(mobileNumber, mobileNumberLoginRequest, userAgent);
    }

    Optional<Otp> possibleOtpResponse =
        otpRepository.findByOtpTargetDeletedAtNull(possibleOtp.get().getOtp(), mobileNumber);
    if (possibleOtpResponse.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MobileNumber.Verify");
    }
    return signInWithMobileNumber(mobileNumber, mobileNumberLoginRequest, userAgent);
  }

  public MobileNumberLoginDto.MobileNumberLoginResponse updateUserMobileNumber(
      MobileNumberLoginDto.UpdateMobileNumberRequest request, String userAgent) {

    if (ObjectUtils.isEmpty(request)
        || StringUtils.isEmpty(request.userName())
        || StringUtils.isEmpty(request.password())
        || StringUtils.isEmpty(request.mobileNumberJwt())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.WrongCredentials");
    }
    String mobileNumber;
    try {
      JWTClaimsSet claimsSet = JWTParser.parse(request.mobileNumberJwt()).getJWTClaimsSet();
      mobileNumber =
          Optional.ofNullable(claimsSet.getClaims().get(MOBILE_NUMBER))
              .map(Object::toString)
              .orElseThrow(() -> new ApiException(InternalErrorCodes.UN_AUTHORIZED, PARSE_TOKEN));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExpiredOrInvalidToken", e);
    }
    List<User> optionalUsers = userRepository.findUsersByMobileNumber(mobileNumber);

    if (optionalUsers.size() >= mobileNumberLoginUsersLimit) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MobileNumber.Users");
    }

    var jwt = userIdpService.loginUser(massage(request.userName()), request.password());
    if (jwt.isBlank()) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.WrongCredentials");
    }

    var user = getUserByUserName(request.userName());
    verifyUserLogin(request.userName(), user);
    user.setMobileNumber(mobileNumber);
    userRepository.save(user);

    return signInWithMobileNumber(
        mobileNumber,
        MobileNumberLoginDto.MobileNumberLoginRequest.builder()
            .firebaseToken(request.firebaseToken())
            .appContext(request.appContext())
            .build(),
        userAgent);
  }

  public MobileNumberLoginDto.PingResponse verifyUserTokenByPing(
      MobileNumberLoginDto.PingRequest pingRequest, String userAgent) {

    Date expiredAt;
    String authUserId;
    try {
      JWTClaimsSet claimsSet = JWTParser.parse(pingRequest.userJwt()).getJWTClaimsSet();
      authUserId =
          Optional.ofNullable(claimsSet.getClaims().get(SUB))
              .map(Object::toString)
              .orElseThrow(() -> new ApiException(InternalErrorCodes.UN_AUTHORIZED, PARSE_TOKEN));
      expiredAt = (Date) claimsSet.getClaims().get(EXP);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExpiredOrInvalidToken", e);
    }

    if (DateTimeUtil.convertIso8601ToEpoch(LocalDate.now().atTime(LocalTime.now()))
        > DateTimeUtil.convertIso8601ToEpoch(DateTimeUtil.getLocalDateTimeFromDate(expiredAt))) {
      return MobileNumberLoginDto.PingResponse.builder()
          .status("Failed")
          .message("Invalid or Expired Token. Please login again!")
          .build();
    }

    User user = getUserByAuthUserId(authUserId);

    saveLoginHistory(
        user,
        LoginMethod.MOBILE_NUMBER,
        Boolean.TRUE.equals(MobileAppUtil.requestComingFromMobileApp(userAgent))
            ? LoginDevice.MOBILE
            : LoginDevice.WEB);
    return MobileNumberLoginDto.PingResponse.builder().status("Success").message("Success").build();
  }

  public boolean isSubjectProfileOrg(String sourceOrg, String targetOrg) {
    Optional<SubjectProfileOrgs.SubjectProfileOrg> optionalSubjectProfileOrgs =
        subjectProfileOrgs.getConfig().stream()
            .filter(o -> o.getSource().equals(sourceOrg))
            .findFirst();
    if (optionalSubjectProfileOrgs.isEmpty()) {
      return false;
    }
    List<String> targetOrgSlugs = optionalSubjectProfileOrgs.get().getTarget();
    return targetOrgSlugs.contains(targetOrg);
  }

  public String buildScormAuthToken() {
    final String authUserId = new JwtIdTokenCredentialsHolder().getAuthUserId();
    final String organization = new JwtIdTokenCredentialsHolder().getOrganization();
    var jwtValidityInDays = 1;
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    return Jwts.builder()
        .claim(SUB, authUserId)
        .claim(ORG, organization)
        .setIssuedAt(Date.from(Instant.now()))
        .setExpiration(
            Date.from(
                Date.from(Instant.now()).toInstant().plus(jwtValidityInDays, ChronoUnit.DAYS)))
        .signWith(SignatureAlgorithm.HS256, tokenSignInKey)
        .compact();
  }

  public boolean isUserBelongingToDeletedOrg(User user) {
    final Organization organization = organizationRepository.findBySlug(user.getOrganization());
    return Objects.nonNull(organization.getDeletedAt());
  }

  public boolean isUserLoginByMobile() {
    var authentication = SecurityContextHolder.getContext().getAuthentication();
    var claims = ((JwtAuthentication) authentication).getJwtClaimsSet();
    return (Boolean) claims.get(JwtConstants.IS_MOBILE);
  }

  public List<String> evaluationPermissionsForUser(User user) {
    return userRoleHelper.getPermissionsForUser(user);
  }

  public String getUniqueUserName(String firstName, String mobileNumber) {
    if (!userRepository.existsByUserName(mobileNumber)) {
      return mobileNumber;
    }
    var userName =
        mobileNumber + "." + StringUtils.deleteWhitespace(firstName.toLowerCase().trim());
    return constructUserNameByMobileNumber(userName);
  }

  private String constructUserNameByMobileNumber(String userName) {
    if (!userRepository.existsByUserName(userName)) {
      return userName;
    }
    return constructUserNameByMobileNumber(userName.concat(getRandomNumber(3)));
  }

  private String getRandomNumber(int digCount) {
    StringBuilder sb = new StringBuilder(digCount);
    for (int i = 0; i < digCount; i++) {
      sb.append((char) ('0' + random.nextInt(10)));
    }
    return sb.toString();
  }
}
